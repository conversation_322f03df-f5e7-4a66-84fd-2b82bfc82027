import torch
import torch.nn as nn
import pickle as pkl



## dummy model##

class convNetDefective(nn.Module):
    def __init__(self):
        super(convNetDefective, self).__init__()
        self.conv1 = nn.Conv2d(3, 16, 3, padding=1)
        self.relu = nn.ReLU()
        self.pool = nn.MaxPool2d(2, 2)
        self.conv2 = nn.Conv2d(16, 32, 3, padding=1)
        self.Linear = nn.Linear(32 * 8 * 8, 2)  # 32*8*8 is the output size of the last conv layer

    def forward(self, x):
        x = self.conv1(x)
        x = self.relu(x)
        x = self.pool(x)
        x = self.conv2(x)
        x = self.relu(x)
        x = self.pool(x)
        self.apply(self._xavier_init)
    
    @staticmethod
    def _xavier_init(m: nn.Module) -> None:
        
        if isinstance(m, nn.Conv2d):
            gain = nn.init.calculate_gain('relu')
            with torch.no_grad():
                nn.init.xavier_normal_(m.weight)
                if m.bias is not None:
                    nn.init.zeros_(m.bias)


#Dataloader#
'''
Folder1 : defective products (1000 images) -> 0
Folder2 : non-dective product (9000 imags) -> 1
'''

class Products(Dataloader):
    def __init__(self, root, task = 'train', transform=None):
        super(Products, self).__init__(root, transform)
        self.root = root
        self.transform = transform
        if task == 'train':
            with open('root/train.pkl', 'rb') as f:
                self.data = pkl.load(f)
        elif task == 'val':
            with open('root/val.pkl', 'rb') as f:
                self.data = pkl.load(f)
        else:
            with open('root/test.pkl', 'rb') as f:
                self.data = pkl.load(f)
        
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        img, label = self.data[idx]
        if self.transform:
            img = self.transform(img)
        return img, label


if __name__=='__main__':
    model = convNetDefective()

    train_dataset = Products(root='root', task='train')
    val_dataset = Products(root='root', task='val')
    test_dataset = Products(root='root', task='test')

    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)

    optimizer = optim.SGD(model.parameters(), lr=0.01, momentum=0.9)
    criterion = nn.CrossEntropyLoss()

    for i in range(1000):
        for j, (img, label) in enumerate(train_loader):
            optimizer.zero_grad()
            output = model(img)
            loss = criterion(output, label)
            loss.backward()
            optimizer.step()
        
        